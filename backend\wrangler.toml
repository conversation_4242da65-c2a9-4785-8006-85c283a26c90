name = "license-verify-backend"
main = "src/index.ts"
compatibility_date = "2025-07-31"

[env.production]
name = "license-verify-backend"

[env.development]
name = "license-verify-backend-dev"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "license_verify_db"
database_id = "90edf588-2fb0-46dd-8dac-4221fc301393"

# KV 存储配置
[[kv_namespaces]]
binding = "CACHE"
id = "b9cfa4d9f00c4650a1a6428fa8cecc4a"

# 环境变量
[vars]
JWT_SECRET = "your-jwt-secret-here"
API_VERSION = "v1"


