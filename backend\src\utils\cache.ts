import { KVNamespace } from '@cloudflare/workers-types';

export class CacheService {
  private kv: KVNamespace;

  constructor(kv: KVNamespace) {
    this.kv = kv;
  }

  // 缓存键生成器
  static keys = {
    licenseVerify: (key: string) => `license:verify:${key}`,
    deviceBinding: (licenseId: number) => `device:binding:${licenseId}`,
    productConfig: (id: number) => `product:config:${id}`,
    adminAuth: (id: number) => `admin:auth:${id}`,
    salesStats: (adminId: number, period: string) => `stats:${adminId}:${period}`,
  };

  async get<T>(key: string): Promise<T | null> {
    const value = await this.kv.get(key);
    return value ? JSON.parse(value) : null;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    const options = ttl ? { expirationTtl: ttl } : undefined;
    await this.kv.put(key, JSON.stringify(value), options);
  }

  async delete(key: string): Promise<void> {
    await this.kv.delete(key);
  }

  async clear(prefix: string): Promise<void> {
    const list = await this.kv.list({ prefix });
    const deletePromises = list.keys.map(key => this.kv.delete(key.name));
    await Promise.all(deletePromises);
  }
}