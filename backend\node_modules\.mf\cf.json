{"clientTcpRtt": 2, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 9370, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "JP", "isEUCountry": false, "region": "Tokyo", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "zewmuf6OKp7o1noJfOfDsrW2oIvYAXlFOqVGNQ+tsCk=", "tlsExportedAuthenticator": {"clientFinished": "544fd40b800bfabf2a8a9428695eb039b51f7cb12a16569b79e3407406d0901bfca4090a86a951da00a95afab0a394be", "clientHandshake": "d6165b7b5e16dfd87be7030ce7e722fbe9d72ac6ec3ddcef30cc9a21082d059fdc1677a86ef199ab49ebd3833b33bbb2", "serverHandshake": "28af177629191128240ea821b6d2107454e698052b32d55be1a28f46c4a6e1a6986c16ed4b35cae1b22f578489d9cc00", "serverFinished": "6e574829b5b84bc3ba2e88beed358e59a9966982e8b5662d988b4a2aa794c59343517090c9fa3a3e82ec6baa7a5377ba"}, "tlsClientHelloLength": "386", "colo": "NRT", "timezone": "Asia/Tokyo", "longitude": "139.69171", "latitude": "35.68950", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "101-8656", "city": "Tokyo", "tlsVersion": "TLSv1.3", "regionCode": "13", "asOrganization": "SAKURA Internet Inc.", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}